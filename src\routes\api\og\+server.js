import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';

export async function GET({ url }) {
	const type = url.searchParams.get('type') || 'static';

	if (type === 'static') {
		return await generateStaticOG();
	}

	// Default fallback
	return await generateStaticOG();
}

async function generateStaticOG() {
	const markup = html`
		<div style="
			display: flex;
			width: 1200px;
			height: 630px;
			background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e1b4b 100%);
			position: relative;
			overflow: hidden;
		">
			<!-- Background gradient blobs -->
			<div style="
				position: absolute;
				top: -100px;
				left: -100px;
				width: 400px;
				height: 400px;
				background: radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%);
				border-radius: 50%;
			"></div>
			<div style="
				position: absolute;
				bottom: -100px;
				right: -100px;
				width: 350px;
				height: 350px;
				background: radial-gradient(circle, rgba(14, 165, 233, 0.3) 0%, transparent 70%);
				border-radius: 50%;
			"></div>
			<div style="
				position: absolute;
				top: 100px;
				right: 50px;
				width: 250px;
				height: 250px;
				background: radial-gradient(circle, rgba(236, 72, 153, 0.2) 0%, transparent 70%);
				border-radius: 50%;
			"></div>

			<!-- Main content container -->
			<div style="
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 100%;
				padding: 60px;
				position: relative;
				z-index: 1;
			">
				<!-- Title -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 40px;
				">
					<h1 style="
						font-size: 72px;
						font-weight: 800;
						color: #f8fafc;
						margin: 0;
						text-align: center;
						line-height: 1.1;
						letter-spacing: -0.02em;
					">anithing.moe</h1>
					<p style="
						font-size: 28px;
						color: #38bdf8;
						margin: 8px 0 0 0;
						text-align: center;
						font-weight: 600;
					">Your Ultimate Japanese Media Hub</p>
				</div>

				<!-- Features grid -->
				<div style="
					display: flex;
					justify-content: space-between;
					width: 100%;
					max-width: 900px;
					margin-bottom: 40px;
				">
					<!-- Feature 1 -->
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 280px;
						padding: 24px;
						background: rgba(30, 41, 59, 0.6);
						border-radius: 16px;
						border: 1px solid rgba(148, 163, 184, 0.2);
					">
						<div style="
							font-size: 48px;
							margin-bottom: 12px;
						">🔍</div>
						<h3 style="
							font-size: 20px;
							font-weight: 700;
							color: #7dd3fc;
							margin: 0 0 8px 0;
							text-align: center;
						">Unified Search</h3>
						<p style="
							font-size: 14px;
							color: #cbd5e1;
							margin: 0;
							text-align: center;
							line-height: 1.4;
						">Find anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB and more</p>
					</div>

					<!-- Feature 2 -->
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 280px;
						padding: 24px;
						background: rgba(30, 41, 59, 0.6);
						border-radius: 16px;
						border: 1px solid rgba(148, 163, 184, 0.2);
					">
						<div style="
							font-size: 48px;
							margin-bottom: 12px;
						">📚</div>
						<h3 style="
							font-size: 20px;
							font-weight: 700;
							color: #7dd3fc;
							margin: 0 0 8px 0;
							text-align: center;
						">Centralized Lists</h3>
						<p style="
							font-size: 14px;
							color: #cbd5e1;
							margin: 0;
							text-align: center;
							line-height: 1.4;
						">Manage your watch, read, and play lists for all services seamlessly</p>
					</div>

					<!-- Feature 3 -->
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 280px;
						padding: 24px;
						background: rgba(30, 41, 59, 0.6);
						border-radius: 16px;
						border: 1px solid rgba(148, 163, 184, 0.2);
					">
						<div style="
							font-size: 48px;
							margin-bottom: 12px;
						">👀</div>
						<h3 style="
							font-size: 20px;
							font-weight: 700;
							color: #7dd3fc;
							margin: 0 0 8px 0;
							text-align: center;
						">Track Friends Aniwhere</h3>
						<p style="
							font-size: 14px;
							color: #cbd5e1;
							margin: 0;
							text-align: center;
							line-height: 1.4;
						">See what your friends are watching and reading across all platforms</p>
					</div>
				</div>

				<!-- Call to action -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
				">
					<p style="
						font-size: 22px;
						color: #e2e8f0;
						margin: 0 0 12px 0;
						text-align: center;
						font-weight: 500;
					">All your media, aniwhere.</p>
					<div style="
						display: flex;
						align-items: center;
						padding: 12px 24px;
						background: linear-gradient(135deg, #3b82f6, #1d4ed8);
						border-radius: 8px;
						box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
					">
						<span style="
							font-size: 18px;
							color: white;
							font-weight: 600;
						">Get Started Today →</span>
					</div>
				</div>
			</div>
		</div>
	`;

	const svg = await satori(markup, {
		width: 1200,
		height: 630,
		fonts: []
	});

	const resvg = new Resvg(svg);
	const pngData = resvg.render();
	const pngBuffer = pngData.asPng();

	return new Response(pngBuffer, {
		headers: {
			'Content-Type': 'image/png',
			'Cache-Control': 'public, max-age=31536000, immutable'
		}
	});
}