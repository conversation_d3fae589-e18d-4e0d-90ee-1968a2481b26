import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';

export async function GET({ url }) {
	const type = url.searchParams.get('type') || 'static';

	if (type === 'static') {
		return await generateStaticOG();
	}

	// Default fallback
	return await generateStaticOG();
}

async function loadFonts() {
	const [interResponse, emojiResponse] = await Promise.all([
		fetch('http://pixeldrain.com/api/file/52yBhNXR'),
		fetch('http://localhost:5173/NotoColorEmoji-Regular.ttf')
	]);

	const interBuffer = await interResponse.arrayBuffer();
	const emojiBuffer = await emojiResponse.arrayBuffer();

	return {
		inter: interBuffer,
		emoji: emojiBuffer
	};
}

async function generateStaticOG() {
	const markup = html`
		<div style="
			display: flex;
			width: 1200px;
			height: 630px;
			background: #111827;
			position: relative;
			overflow: hidden;
		">
			<!-- Background gradient blobs -->
			<div style="
				position: absolute;
				top: -60px;
				left: -60px;
				width: 300px;
				height: 300px;
				background: radial-gradient(circle, rgba(147, 51, 234, 0.3) 0%, transparent 70%);
				border-radius: 50%;
			"></div>
			<div style="
				position: absolute;
				bottom: -60px;
				right: -60px;
				width: 250px;
				height: 250px;
				background: radial-gradient(circle, rgba(14, 165, 233, 0.3) 0%, transparent 70%);
				border-radius: 50%;
			"></div>
			<div style="
				position: absolute;
				top: 120px;
				right: 30px;
				width: 180px;
				height: 180px;
				background: radial-gradient(circle, rgba(236, 72, 153, 0.2) 0%, transparent 70%);
				border-radius: 50%;
			"></div>

			<!-- Main content -->
			<div style="
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 100%;
				padding: 40px;
				position: relative;
			">
				<!-- Header -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<h1 style="
						font-family: 'Inter';
						font-size: 56px;
						font-weight: 800;
						color: #f8fafc;
						margin: 0 0 12px 0;
						text-align: center;
						line-height: 1.1;
						letter-spacing: -0.02em;
					">anithing.moe</h1>
				</div>

				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<p style="
						font-family: 'Inter';
						font-size: 18px;
						color: #cbd5e1;
						margin: 0;
						text-align: center;
						line-height: 1.4;
						max-width: 600px;
					">Your ultimate gateway to the world of Japanese media. Search, track, and manage all your lists in one unified experience.</p>
				</div>

				<!-- What Anithing.moe Offers -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 24px;
				">
					<h2 style="
						font-family: 'Inter';
						font-size: 24px;
						font-weight: 600;
						color: #38bdf8;
						margin: 0 0 20px 0;
						text-align: center;
					">What Anithing.moe Offers</h2>
				</div>

				<!-- Features grid -->
				<div style="
					display: flex;
					justify-content: center;
					gap: 20px;
					margin-bottom: 24px;
					max-width: 900px;
				">
					<!-- Feature 1 -->
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 260px;
						padding: 20px;
						background: rgba(31, 41, 55, 0.5);
						border-radius: 12px;
						border: 1px solid rgba(75, 85, 99, 0.5);
					">
						<div style="
							font-family: 'NotoColorEmoji';
							font-size: 32px;
							margin-bottom: 8px;
						">🔍</div>
						<h3 style="
							font-family: 'Inter';
							font-size: 16px;
							font-weight: 600;
							color: #7dd3fc;
							margin: 0 0 6px 0;
							text-align: center;
						">Unified Search</h3>
						<p style="
							font-family: 'Inter';
							font-size: 12px;
							color: #cbd5e1;
							margin: 0;
							text-align: center;
							line-height: 1.3;
						">Find anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB and more from one place.</p>
					</div>

					<!-- Feature 2 -->
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 260px;
						padding: 20px;
						background: rgba(31, 41, 55, 0.5);
						border-radius: 12px;
						border: 1px solid rgba(75, 85, 99, 0.5);
					">
						<div style="
							font-family: 'NotoColorEmoji';
							font-size: 32px;
							margin-bottom: 8px;
						">📚</div>
						<h3 style="
							font-family: 'Inter';
							font-size: 16px;
							font-weight: 600;
							color: #7dd3fc;
							margin: 0 0 6px 0;
							text-align: center;
						">Centralized Lists</h3>
						<p style="
							font-family: 'Inter';
							font-size: 12px;
							color: #cbd5e1;
							margin: 0;
							text-align: center;
							line-height: 1.3;
						">Manage your watch, read, and play lists for all services seamlessly.</p>
					</div>

					<!-- Feature 3 -->
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 260px;
						padding: 20px;
						background: rgba(31, 41, 55, 0.5);
						border-radius: 12px;
						border: 1px solid rgba(75, 85, 99, 0.5);
					">
						<div style="
							font-family: 'NotoColorEmoji';
							font-size: 32px;
							margin-bottom: 8px;
						">👀</div>
						<h3 style="
							font-family: 'Inter';
							font-size: 16px;
							font-weight: 600;
							color: #7dd3fc;
							margin: 0 0 6px 0;
							text-align: center;
						">Track Friends Aniwhere</h3>
						<p style="
							font-family: 'Inter';
							font-size: 12px;
							color: #cbd5e1;
							margin: 0;
							text-align: center;
							line-height: 1.3;
						">See what your friends are watching and reading across all platforms with real-time activity feeds.</p>
					</div>
				</div>

				<!-- Footer tagline -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
				">
					<p style="
						font-family: 'Inter';
						font-size: 14px;
						color: #9ca3af;
						margin: 0;
						text-align: center;
					">All your media, aniwhere.</p>
				</div>
			</div>
		</div>
	`;

	const fonts = await loadFonts();

	const svg = await satori(markup, {
		width: 1200,
		height: 630,
		fonts: [
			{
				name: 'Inter',
				data: fonts.inter,
				weight: 400,
				style: 'normal',
			},
			{
				name: 'NotoColorEmoji',
				data: fonts.emoji,
				weight: 400,
				style: 'normal',
			},
		]
	});

	const resvg = new Resvg(svg);
	const pngData = resvg.render();
	const pngBuffer = pngData.asPng();

	return new Response(pngBuffer, {
		headers: {
			'Content-Type': 'image/png',
			'Cache-Control': 'public, max-age=31536000, immutable'
		}
	});
}