import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';

export async function GET({ url }) {
	const type = url.searchParams.get('type') || 'static';

	if (type === 'static') {
		return await generateStaticOG();
	}

	// Default fallback
	return await generateStaticOG();
}

async function loadFonts() {
	const [interResponse, emojiResponse] = await Promise.all([
		fetch('http://pixeldrain.com/api/file/52yBhNXR'),
		fetch('http://localhost:5173/NotoColorEmoji-Regular.ttf')
	]);

	const interBuffer = await interResponse.arrayBuffer();
	const emojiBuffer = await emojiResponse.arrayBuffer();

	return {
		inter: interBuffer,
		emoji: emojiBuffer
	};
}

async function generateStaticOG() {
	const markup = html`
		<div style="
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 1200px;
			height: 630px;
			background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e1b4b 100%);
			padding: 60px;
		">
				<!-- Title -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 40px;
				">
					<h1 style="
						font-family: 'Inter';
						font-size: 72px;
						font-weight: 800;
						color: #f8fafc;
						margin: 0 0 8px 0;
						text-align: center;
						line-height: 1.1;
						letter-spacing: -0.02em;
					">anithing.moe</h1>
				</div>

				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 40px;
				">
					<p style="
						font-family: 'Inter';
						font-size: 28px;
						color: #38bdf8;
						margin: 0;
						text-align: center;
						font-weight: 600;
					">Your Ultimate Japanese Media Hub</p>
				</div>

				<!-- Features summary -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 40px;
				">
					<p style="
						font-family: 'Inter';
						font-size: 24px;
						color: #7dd3fc;
						margin: 0 0 16px 0;
						text-align: center;
						font-weight: 600;
					">🔍 Unified Search • 📚 Centralized Lists • 👀 Track Friends Aniwhere</p>
				</div>

				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 40px;
				">
					<p style="
						font-family: 'Inter';
						font-size: 18px;
						color: #cbd5e1;
						margin: 0;
						text-align: center;
						line-height: 1.4;
						max-width: 800px;
					">Find anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB and more. Manage all your lists and track friends across all platforms seamlessly.</p>
				</div>

				<!-- Call to action -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
				">
					<p style="
						font-family: 'Inter';
						font-size: 22px;
						color: #e2e8f0;
						margin: 0 0 12px 0;
						text-align: center;
						font-weight: 500;
					">All your media, aniwhere.</p>
				</div>

				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
				">
					<div style="
						display: flex;
						align-items: center;
						padding: 12px 24px;
						background: linear-gradient(135deg, #3b82f6, #1d4ed8);
						border-radius: 8px;
						box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
					">
						<span style="
							font-family: 'Inter';
							font-size: 18px;
							color: white;
							font-weight: 600;
						">Get Started Today →</span>
					</div>
				</div>
			</div>
		`;

	const fontData = await loadFont();

	const svg = await satori(markup, {
		width: 1200,
		height: 630,
		fonts: [
			{
				name: 'Inter',
				data: fontData,
				weight: 400,
				style: 'normal',
			},
		]
	});

	const resvg = new Resvg(svg);
	const pngData = resvg.render();
	const pngBuffer = pngData.asPng();

	return new Response(pngBuffer, {
		headers: {
			'Content-Type': 'image/png',
			'Cache-Control': 'public, max-age=31536000, immutable'
		}
	});
}